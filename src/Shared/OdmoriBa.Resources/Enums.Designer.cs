﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace OdmoriBa.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Enums {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Enums() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("OdmoriBa.Resources.Enums", typeof(Enums).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        public static string TravelerStatus_Requested {
            get {
                return ResourceManager.GetString("TravelerStatus_Requested", resourceCulture);
            }
        }
        
        public static string TravelerStatus_Draft {
            get {
                return ResourceManager.GetString("TravelerStatus_Draft", resourceCulture);
            }
        }
        
        public static string TravelerStatus_Confirmed {
            get {
                return ResourceManager.GetString("TravelerStatus_Confirmed", resourceCulture);
            }
        }
        
        public static string TravelerStatus_Cancelled {
            get {
                return ResourceManager.GetString("TravelerStatus_Cancelled", resourceCulture);
            }
        }
        
        public static string UserStatus_Active {
            get {
                return ResourceManager.GetString("UserStatus_Active", resourceCulture);
            }
        }
        
        public static string UserStatus_Inactive {
            get {
                return ResourceManager.GetString("UserStatus_Inactive", resourceCulture);
            }
        }
        
        public static string TripBusDirection_Departure {
            get {
                return ResourceManager.GetString("TripBusDirection_Departure", resourceCulture);
            }
        }
        
        public static string TripBusDirection_Return {
            get {
                return ResourceManager.GetString("TripBusDirection_Return", resourceCulture);
            }
        }
    }
}
