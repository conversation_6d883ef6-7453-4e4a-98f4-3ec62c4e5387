﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <UserSecretsId>************************************</UserSecretsId>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="CodeBeam.MudBlazor.Extensions" Version="8.2.4" />
        <PackageReference Include="Heron.MudCalendar" Version="3.2.0" />
        <PackageReference Include="Microsoft.Identity.Web" Version="3.11.0" />
        <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.11.0" />
        <PackageReference Include="MudBlazor" Version="8.10.0" />
    </ItemGroup>


    <ItemGroup>
        <ProjectReference Include="..\..\Application\OdmoriBa.JobProcessing\OdmoriBa.JobProcessing.csproj"/>
        <ProjectReference Include="..\OdmoriBa.Presentation\OdmoriBa.Presentation.csproj"/>
    </ItemGroup>

</Project>
