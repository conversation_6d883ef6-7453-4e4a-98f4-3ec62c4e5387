﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
        <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Mvc" Version="1.5.0" />
        <PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.3.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.OpenTelemetry" Version="4.2.0" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.12"/>
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Application\OdmoriBa.Application\OdmoriBa.Application.csproj" />
        <ProjectReference Include="..\..\Infrastructure\OdmoriBa.Infrastructure\OdmoriBa.Infrastructure.csproj" />
    </ItemGroup>

</Project>
