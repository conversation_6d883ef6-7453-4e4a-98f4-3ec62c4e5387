﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <UserSecretsId>************************************</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Scalar.AspNetCore" Version="2.6.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\OdmoriBa.Presentation\OdmoriBa.Presentation.csproj"/>
    </ItemGroup>

</Project>
