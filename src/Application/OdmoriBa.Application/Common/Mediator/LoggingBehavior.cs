﻿namespace OdmoriBa.Application.Common.Mediator;

public sealed class LoggingBehavior<TMessage, TResponse>(ILogger<LoggingBehavior<TMessage, TResponse>> logger)
    : IPipelineBehavior<TMessage, TResponse>
    where TMessage : IMessage
{
    public async ValueTask<TResponse> Handle(TMessage message, MessageHandlerDelegate<TMessage, TResponse> next,
        CancellationToken cancellationToken)
    {
        TResponse response;
        logger.LogInformation("{RequestName} - STARTED. Request body: {@RequestBody}", typeof(TMessage).Name, message);

        try
        {
            response = await next(message, cancellationToken);
            if (response is Result { IsError: true } result)
            {
                logger.Log(result.Error.Type is ErrorType.Failure ? LogLevel.Error : LogLevel.Information,
                    "Error on {RequestName} - Type: {Type} | Code: {Code} | Description: {Description}",
                    typeof(TMessage).Name, result.Error.Type, result.Error.Code, result.Error.Description);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "{RequestName} - INTERNAL SERVER ERROR: {ErrorMessage}", typeof(TMessage).Name, ex.Message);
            throw;
        }
        finally
        {
            logger.LogInformation("{RequestName} - FINISHED", typeof(TMessage).Name);
        }

        return response;
    }
}