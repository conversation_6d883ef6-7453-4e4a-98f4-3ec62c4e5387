﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OdmoriBa.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial8 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "deleted_at",
                table: "loyalty_cards",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "is_deleted",
                table: "loyalty_cards",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "deleted_at",
                table: "loyalty_cards");

            migrationBuilder.DropColumn(
                name: "is_deleted",
                table: "loyalty_cards");
        }
    }
}
