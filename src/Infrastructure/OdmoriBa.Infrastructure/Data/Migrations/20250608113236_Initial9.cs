﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OdmoriBa.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial9 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_device_bindings_users_id",
                table: "device_bindings");

            migrationBuilder.RenameColumn(
                name: "token",
                table: "device_bindings",
                newName: "push_notification_token");

            migrationBuilder.AlterColumn<string>(
                name: "token",
                table: "push_notification_outbox",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "deleted_at",
                table: "payments",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "is_deleted",
                table: "payments",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "deleted_at",
                table: "loyalty_point_transactions",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "is_deleted",
                table: "loyalty_point_transactions",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "app_version",
                table: "device_bindings",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "deleted_at",
                table: "device_bindings",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "device_id",
                table: "device_bindings",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "device_model",
                table: "device_bindings",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "is_deleted",
                table: "device_bindings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "os_version",
                table: "device_bindings",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<Guid>(
                name: "user_id",
                table: "device_bindings",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "ix_device_bindings_user_id",
                table: "device_bindings",
                column: "user_id");

            migrationBuilder.AddForeignKey(
                name: "fk_device_bindings_users_user_id",
                table: "device_bindings",
                column: "user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_device_bindings_users_user_id",
                table: "device_bindings");

            migrationBuilder.DropIndex(
                name: "ix_device_bindings_user_id",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                table: "payments");

            migrationBuilder.DropColumn(
                name: "is_deleted",
                table: "payments");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                table: "loyalty_point_transactions");

            migrationBuilder.DropColumn(
                name: "is_deleted",
                table: "loyalty_point_transactions");

            migrationBuilder.DropColumn(
                name: "app_version",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "deleted_at",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "device_id",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "device_model",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "is_deleted",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "os_version",
                table: "device_bindings");

            migrationBuilder.DropColumn(
                name: "user_id",
                table: "device_bindings");

            migrationBuilder.RenameColumn(
                name: "push_notification_token",
                table: "device_bindings",
                newName: "token");

            migrationBuilder.AlterColumn<string>(
                name: "token",
                table: "push_notification_outbox",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddForeignKey(
                name: "fk_device_bindings_users_id",
                table: "device_bindings",
                column: "id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
