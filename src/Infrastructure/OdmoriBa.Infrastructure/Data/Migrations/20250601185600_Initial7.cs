﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OdmoriBa.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial7 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_travelers_travel_parties_travel_party_id",
                table: "travelers");

            migrationBuilder.AddForeignKey(
                name: "fk_travelers_travel_parties_travel_party_id",
                table: "travelers",
                column: "travel_party_id",
                principalTable: "travel_parties",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_travelers_travel_parties_travel_party_id",
                table: "travelers");

            migrationBuilder.AddForeignKey(
                name: "fk_travelers_travel_parties_travel_party_id",
                table: "travelers",
                column: "travel_party_id",
                principalTable: "travel_parties",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
