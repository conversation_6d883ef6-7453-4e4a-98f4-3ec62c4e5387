﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace OdmoriBa.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "loyalty_can_spend_points",
                table: "trip_destinations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "loyalty_maximum_points_to_spend",
                table: "trip_destinations",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "loyalty_points",
                table: "trip_destinations",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "loyalty_can_spend_points",
                table: "trip_destinations");

            migrationBuilder.DropColumn(
                name: "loyalty_maximum_points_to_spend",
                table: "trip_destinations");

            migrationBuilder.DropColumn(
                name: "loyalty_points",
                table: "trip_destinations");
        }
    }
}
